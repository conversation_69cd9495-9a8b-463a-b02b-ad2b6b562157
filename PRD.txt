產品需求文件 (PRD)

1. 簡介
本文件旨在說明開發一個應用程式，此應用程式能夠從現有資料庫中提取特定資料，並將其自動生成為 LibreOffice Impress 的單頁投影片。

2. 開發環境
   後端框架: .NET Core 8
   資料庫: Microsoft SQL Server

3. 功能需求
3.1 資料來源
   資料庫類型: MS SQL Server
   資料庫名稱: YMDB
   連線字串: .\SQL2022
   使用者帳號: sa
   密碼: xxx
   查詢語法 (用於擷取資料):
   SQL
   SELECT TOP (10) CollectNo, CollecAcc, CollectName, CollectId, Tel, Zipcode, Addr, BelongUnit, BelongAcc
   FROM Payee
   WHERE (CollectId <> '') AND (Addr <> '') AND (Tel <> '')
   備註：此查詢語法將用於從 Payee 表中擷取前 10 筆 CollectId、Addr、Tel 皆不為空的資料。

3.2 投影片生成
   輸出格式: LibreOffice Impress (單頁投影片)
   內容: 將上述查詢結果的資料呈現於單頁投影片中。
