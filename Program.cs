
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using Microsoft.Data.SqlClient;
using Syncfusion.Presentation;
using System.Drawing;

namespace GenSlide
{
    class Program
    {
        static void Main(string[] args)
        {
            #region Database Fetch
            // --- This region is for fetching data from the database. ---
            // --- You should ensure the connection string and credentials are correct.---

            // Database connection details - PLEASE VERIFY THIS IS CORRECT
            string connectionString = @"Data Source=.\SQL2022;Initial Catalog=YMDB;User ID=sa;Password=********;Encrypt=True;TrustServerCertificate=True";
            string query = "SELECT TOP 5 CollectNo, CollecAcc, CollectName, CollectId, Tel, Zipcode, Addr, BelongUnit, BelongAcc FROM Payee WHERE (CollectId <> '') AND (Addr <> '') AND (Tel <> '')";

            List<Payee> payees = new List<Payee>();
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                payees.Add(new Payee
                                {
                                    CollectNo = reader.IsDBNull(reader.GetOrdinal("CollectNo")) ? null : reader["CollectNo"].ToString(),
                                    CollecAcc = reader.IsDBNull(reader.GetOrdinal("CollecAcc")) ? null : reader["CollecAcc"].ToString(),
                                    CollectName = reader.IsDBNull(reader.GetOrdinal("CollectName")) ? null : reader["CollectName"].ToString(),
                                    CollectId = reader.IsDBNull(reader.GetOrdinal("CollectId")) ? null : reader["CollectId"].ToString(),
                                    Tel = reader.IsDBNull(reader.GetOrdinal("Tel")) ? null : reader["Tel"].ToString(),
                                    Zipcode = reader.IsDBNull(reader.GetOrdinal("Zipcode")) ? null : reader["Zipcode"].ToString(),
                                    Addr = reader.IsDBNull(reader.GetOrdinal("Addr")) ? null : reader["Addr"].ToString(),
                                    BelongUnit = reader.IsDBNull(reader.GetOrdinal("BelongUnit")) ? null : reader["BelongUnit"].ToString(),
                                    BelongAcc = reader.IsDBNull(reader.GetOrdinal("BelongAcc")) ? null : reader["BelongAcc"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Database Error: {ex.Message}");
                Console.WriteLine("Please ensure the database is running and the connection details are correct.");
                return;
            }

            if (payees.Count == 0)
            {
                Console.WriteLine("No data found in the database that matches the query.");
                return;
            }
            #endregion

            // Generate CSV data file
            string csvFilePath = Path.Combine(Directory.GetCurrentDirectory(), "payee_data.csv");
            GenerateCsvFile(payees, csvFilePath);
            Console.WriteLine($"Generated CSV data file: {csvFilePath}");

            // Convert OTP to ODP using LibreOffice
            try
            {
                string libreOfficePath = "C:\\Program Files\\LibreOffice\\program\\soffice.exe";
                string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "output.otp");
                string odpOutputPath = Path.Combine(Directory.GetCurrentDirectory(), "output.odp");

                if (!File.Exists(libreOfficePath))
                {
                    Console.WriteLine($"LibreOffice not found at: {libreOfficePath}");
                    Console.WriteLine("Skipping ODP generation.");
                    return;
                }

                if (!File.Exists(templatePath))
                {
                    Console.WriteLine($"Template file not found at: {templatePath}");
                    Console.WriteLine("Please ensure 'output.otp' exists in the current directory.");
                    return;
                }

                // First, try simple conversion from OTP to ODP
                ProcessStartInfo startInfo = new ProcessStartInfo();
                startInfo.FileName = libreOfficePath;
                startInfo.UseShellExecute = false;
                startInfo.RedirectStandardOutput = true;
                startInfo.RedirectStandardError = true;
                startInfo.CreateNoWindow = true;

                // Simple conversion command
                startInfo.ArgumentList.Add("--headless");
                startInfo.ArgumentList.Add("--convert-to");
                startInfo.ArgumentList.Add("odp");
                startInfo.ArgumentList.Add("--outdir");
                startInfo.ArgumentList.Add(Directory.GetCurrentDirectory());
                startInfo.ArgumentList.Add(templatePath);

                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit();
                    string stdout = process.StandardOutput.ReadToEnd();
                    string stderr = process.StandardError.ReadToEnd();

                    Console.WriteLine($"LibreOffice Exit Code: {process.ExitCode}");
                    if (!string.IsNullOrEmpty(stdout))
                        Console.WriteLine($"Standard Output: {stdout}");
                    if (!string.IsNullOrEmpty(stderr))
                        Console.WriteLine($"Standard Error: {stderr}");

                    if (process.ExitCode == 0)
                    {
                        // Check if output file was created
                        string expectedOutput = Path.Combine(Directory.GetCurrentDirectory(), "output.odp");
                        if (File.Exists(expectedOutput))
                        {
                            Console.WriteLine("Successfully generated ODP from template.");
                            Console.WriteLine($"ODP file location: {expectedOutput}");
                        }
                        else
                        {
                            Console.WriteLine("Conversion completed but output file not found.");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Error generating ODP. Exit Code: {process.ExitCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during ODP generation: {ex.Message}");
            }
        }

        private static void GenerateCsvFile(List<Payee> payees, string filePath)
        {
            StringBuilder csvContent = new StringBuilder();

            // Add CSV header
            csvContent.AppendLine("CollectNo,CollecAcc,CollectName,CollectId,Tel,Zipcode,Addr,BelongUnit,BelongAcc");

            // Add data rows
            foreach (var payee in payees)
            {
                string[] fields = new string[]
                {
                    EscapeCsvField(payee.CollectNo),
                    EscapeCsvField(payee.CollecAcc),
                    EscapeCsvField(payee.CollectName),
                    EscapeCsvField(payee.CollectId),
                    EscapeCsvField(payee.Tel),
                    EscapeCsvField(payee.Zipcode),
                    EscapeCsvField(payee.Addr),
                    EscapeCsvField(payee.BelongUnit),
                    EscapeCsvField(payee.BelongAcc)
                };
                csvContent.AppendLine(string.Join(",", fields));
            }

            File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);
        }

        private static string EscapeCsvField(string? field)
        {
            if (field == null) return string.Empty;
            string escapedField = field.Replace("\"", "\"\""); // Escape double quotes
            if (escapedField.Contains(",") || escapedField.Contains("\n") || escapedField.Contains("\r") || escapedField.Contains("\""))
            {
                return "\"" + escapedField + "\""; // Enclose in double quotes if it contains comma, newline, or double quote
            }
            return escapedField;
        }
    }

    public class Payee
    {
        public string? CollectNo { get; set; }
        public string? CollecAcc { get; set; }
        public string? CollectName { get; set; }
        public string? CollectId { get; set; }
        public string? Tel { get; set; }
        public string? Zipcode { get; set; }
        public string? Addr { get; set; }
        public string? BelongUnit { get; set; }
        public string? BelongAcc { get; set; }
    }
}
