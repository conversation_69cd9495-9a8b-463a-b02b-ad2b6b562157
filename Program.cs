
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using Microsoft.Data.SqlClient;
using System.IO.Compression;
using System.Xml;
using System.Xml.Linq;
using Syncfusion.Presentation;
using System.Drawing;

namespace GenSlide
{
    class Program
    {
        static void Main(string[] args)
        {
            #region Database Fetch
            // --- This region is for fetching data from the database. ---
            // --- You should ensure the connection string and credentials are correct.---

            // Database connection details - PLEASE VERIFY THIS IS CORRECT
            string connectionString = @"Data Source=.\SQL2022;Initial Catalog=YMDB;User ID=sa;Password=********;Encrypt=True;TrustServerCertificate=True";
            string query = "SELECT TOP 5 CollectNo, CollecAcc, CollectName, CollectId, Tel, Zipcode, Addr, BelongUnit, BelongAcc FROM Payee WHERE (CollectId <> '') AND (Addr <> '') AND (Tel <> '')";

            List<Payee> payees = new List<Payee>();
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                payees.Add(new Payee
                                {
                                    CollectNo = reader.IsDBNull(reader.GetOrdinal("CollectNo")) ? null : reader["CollectNo"].ToString(),
                                    CollecAcc = reader.IsDBNull(reader.GetOrdinal("CollecAcc")) ? null : reader["CollecAcc"].ToString(),
                                    CollectName = reader.IsDBNull(reader.GetOrdinal("CollectName")) ? null : reader["CollectName"].ToString(),
                                    CollectId = reader.IsDBNull(reader.GetOrdinal("CollectId")) ? null : reader["CollectId"].ToString(),
                                    Tel = reader.IsDBNull(reader.GetOrdinal("Tel")) ? null : reader["Tel"].ToString(),
                                    Zipcode = reader.IsDBNull(reader.GetOrdinal("Zipcode")) ? null : reader["Zipcode"].ToString(),
                                    Addr = reader.IsDBNull(reader.GetOrdinal("Addr")) ? null : reader["Addr"].ToString(),
                                    BelongUnit = reader.IsDBNull(reader.GetOrdinal("BelongUnit")) ? null : reader["BelongUnit"].ToString(),
                                    BelongAcc = reader.IsDBNull(reader.GetOrdinal("BelongAcc")) ? null : reader["BelongAcc"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Database Error: {ex.Message}");
                Console.WriteLine("Please ensure the database is running and the connection details are correct.");
                return;
            }

            if (payees.Count == 0)
            {
                Console.WriteLine("No data found in the database that matches the query.");
                return;
            }
            #endregion

            // Generate ODP directly from template with data
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "output.otp");
            string odpOutputPath = Path.Combine(Directory.GetCurrentDirectory(), "output.odp");

            // Create a modified OTP with data embedded
            string modifiedTemplatePath = Path.Combine(Directory.GetCurrentDirectory(), "output_with_data.otp");
            CreateOtpWithData(payees, templatePath, modifiedTemplatePath);
            Console.WriteLine($"Created template with data: {modifiedTemplatePath}");

            // Convert modified OTP to ODP using LibreOffice
            try
            {
                string libreOfficePath = "C:\\Program Files\\LibreOffice\\program\\soffice.exe";

                if (!File.Exists(libreOfficePath))
                {
                    Console.WriteLine($"LibreOffice not found at: {libreOfficePath}");
                    Console.WriteLine("Skipping ODP generation.");
                    return;
                }

                if (!File.Exists(modifiedTemplatePath))
                {
                    Console.WriteLine($"Modified template file not found at: {modifiedTemplatePath}");
                    return;
                }

                // Convert modified OTP to ODP
                ProcessStartInfo startInfo = new ProcessStartInfo();
                startInfo.FileName = libreOfficePath;
                startInfo.UseShellExecute = false;
                startInfo.RedirectStandardOutput = true;
                startInfo.RedirectStandardError = true;
                startInfo.CreateNoWindow = true;

                // Simple conversion command
                startInfo.ArgumentList.Add("--headless");
                startInfo.ArgumentList.Add("--convert-to");
                startInfo.ArgumentList.Add("odp");
                startInfo.ArgumentList.Add("--outdir");
                startInfo.ArgumentList.Add(Directory.GetCurrentDirectory());
                startInfo.ArgumentList.Add(modifiedTemplatePath);

                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit();
                    string stdout = process.StandardOutput.ReadToEnd();
                    string stderr = process.StandardError.ReadToEnd();

                    Console.WriteLine($"LibreOffice Exit Code: {process.ExitCode}");
                    if (!string.IsNullOrEmpty(stdout))
                        Console.WriteLine($"Standard Output: {stdout}");
                    if (!string.IsNullOrEmpty(stderr))
                        Console.WriteLine($"Standard Error: {stderr}");

                    if (process.ExitCode == 0)
                    {
                        // Check if output file was created
                        string expectedOutput = Path.Combine(Directory.GetCurrentDirectory(), "output_with_data.odp");
                        if (File.Exists(expectedOutput))
                        {
                            // Try to rename to final output name
                            try
                            {
                                if (File.Exists(odpOutputPath))
                                {
                                    // Wait a bit and try to delete
                                    System.Threading.Thread.Sleep(1000);
                                    File.Delete(odpOutputPath);
                                }
                                File.Move(expectedOutput, odpOutputPath);
                                Console.WriteLine("Successfully generated ODP with embedded data.");
                                Console.WriteLine($"ODP file location: {odpOutputPath}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"File created but couldn't rename: {ex.Message}");
                                Console.WriteLine($"ODP file location: {expectedOutput}");
                            }

                            // Clean up temporary template
                            try
                            {
                                if (File.Exists(modifiedTemplatePath))
                                    File.Delete(modifiedTemplatePath);
                            }
                            catch
                            {
                                // Ignore cleanup errors
                            }
                        }
                        else
                        {
                            Console.WriteLine("Conversion completed but output file not found.");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Error generating ODP. Exit Code: {process.ExitCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during ODP generation: {ex.Message}");
            }
        }

        private static void CreateOtpWithData(List<Payee> payees, string templatePath, string outputPath)
        {
            // Copy the original template
            File.Copy(templatePath, outputPath, true);

            // Open the OTP file as a ZIP archive and modify content.xml
            using (var archive = ZipFile.Open(outputPath, ZipArchiveMode.Update))
            {
                var contentEntry = archive.GetEntry("content.xml");
                if (contentEntry == null)
                {
                    Console.WriteLine("content.xml not found in template");
                    return;
                }

                // Read the content.xml
                string contentXml;
                using (var stream = contentEntry.Open())
                using (var reader = new StreamReader(stream))
                {
                    contentXml = reader.ReadToEnd();
                }

                // Delete the old entry
                contentEntry.Delete();

                // Modify the XML to include data
                string modifiedXml = InsertDataIntoXml(contentXml, payees);

                // Create new content.xml entry
                var newContentEntry = archive.CreateEntry("content.xml");
                using (var stream = newContentEntry.Open())
                using (var writer = new StreamWriter(stream))
                {
                    writer.Write(modifiedXml);
                }
            }
        }

        private static string InsertDataIntoXml(string contentXml, List<Payee> payees)
        {
            try
            {
                if (payees == null || payees.Count == 0)
                {
                    Console.WriteLine("No payee data available for replacement");
                    return contentXml;
                }

                // Parse the XML document
                var doc = XDocument.Parse(contentXml);
                var presentationNs = XNamespace.Get("urn:oasis:names:tc:opendocument:xmlns:presentation:1.0");
                var drawNs = XNamespace.Get("urn:oasis:names:tc:opendocument:xmlns:drawing:1.0");

                // Find the presentation body
                var presentationBody = doc.Descendants().FirstOrDefault(e => e.Name.LocalName == "presentation");
                if (presentationBody == null)
                {
                    Console.WriteLine("Could not find presentation element");
                    return contentXml;
                }

                // Find the first slide (template slide)
                var templateSlide = presentationBody.Descendants().FirstOrDefault(e => e.Name.LocalName == "page");
                if (templateSlide == null)
                {
                    Console.WriteLine("Could not find template slide");
                    return contentXml;
                }

                // Remove the original template slide from the presentation
                templateSlide.Remove();

                // Create slides for each payee record
                for (int i = 0; i < payees.Count; i++)
                {
                    var payee = payees[i];

                    // Clone the template slide
                    var newSlide = new XElement(templateSlide);

                    // Update slide name
                    var nameAttr = newSlide.Attribute(drawNs + "name");
                    if (nameAttr != null)
                    {
                        nameAttr.Value = $"page{i + 1}";
                    }

                    // Replace placeholders in this slide
                    string slideXml = newSlide.ToString();
                    slideXml = ReplaceTextInXml(slideXml, "{{CollectName}}", payee.CollectName ?? "");
                    slideXml = ReplaceTextInXml(slideXml, "{{Tel}}", payee.Tel ?? "");

                    // Parse the modified slide XML and add to presentation
                    var modifiedSlide = XElement.Parse(slideXml);
                    presentationBody.Add(modifiedSlide);

                    Console.WriteLine($"Created slide {i + 1}: {{{{CollectName}}}} = {payee.CollectName}, {{{{Tel}}}} = {payee.Tel}");
                }

                return doc.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error modifying XML: {ex.Message}");
                return contentXml; // Return original if modification fails
            }
        }

        private static string ReplaceTextInXml(string xmlContent, string oldText, string newText)
        {
            // Simple text replacement in XML content
            // This preserves the XML structure while replacing text content
            return xmlContent.Replace(oldText, newText);
        }


    }

    public class Payee
    {
        public string? CollectNo { get; set; }
        public string? CollecAcc { get; set; }
        public string? CollectName { get; set; }
        public string? CollectId { get; set; }
        public string? Tel { get; set; }
        public string? Zipcode { get; set; }
        public string? Addr { get; set; }
        public string? BelongUnit { get; set; }
        public string? BelongAcc { get; set; }
    }
}
