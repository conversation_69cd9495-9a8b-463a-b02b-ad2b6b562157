{"format": 1, "restore": {"D:\\Code\\GenSlide\\GenSlide.csproj": {}}, "projects": {"D:\\Code\\GenSlide\\GenSlide.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\GenSlide\\GenSlide.csproj", "projectName": "GenSlide", "projectPath": "D:\\Code\\GenSlide\\GenSlide.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\GenSlide\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.1.0, )"}, "Syncfusion.Presentation.Net.Core": {"target": "Package", "version": "[30.1.42, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}